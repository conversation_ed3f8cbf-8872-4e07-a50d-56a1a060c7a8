package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.ProductType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;

@Entity
@Table(name = "project_limit_config")
public class ProjectLimitConfig extends BaseEntity {

    /**
     * 资产方
     */
    private String flowChannel;

    /**
     * 融单方
     */
    private String guaranteeComp;

    /**
     * 资金方
     */
    private String bankChannel;


    /**
     * 是否含权
     */
    private String isIncludingEquity;
    /**
     * 授信日限额
     */
    private BigDecimal creditDayLimit;
    /**
     * 放款日限额
     */
    private BigDecimal loanDayLimit;
    /**
     * 是否启用
     */
    private Boolean enabled;

    @Enumerated(EnumType.STRING)
    private ProductType productName;

    public String getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(String flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getGuaranteeComp() {
        return guaranteeComp;
    }

    public void setGuaranteeComp(String guaranteeComp) {
        this.guaranteeComp = guaranteeComp;
    }

    public String getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(String bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getIsIncludingEquity() {
        return isIncludingEquity;
    }

    public void setIsIncludingEquity(String isIncludingEquity) {
        this.isIncludingEquity = isIncludingEquity;
    }

    public BigDecimal getCreditDayLimit() {
        return creditDayLimit;
    }

    public void setCreditDayLimit(BigDecimal creditDayLimit) {
        this.creditDayLimit = creditDayLimit;
    }

    public BigDecimal getLoanDayLimit() {
        return loanDayLimit;
    }

    public void setLoanDayLimit(BigDecimal loanDayLimit) {
        this.loanDayLimit = loanDayLimit;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public ProductType getProductName() {
        return productName;
    }

    public void setProductName(ProductType productName) {
        this.productName = productName;
    }
}
