package com.maguo.loan.cash.flow.job.withhold;


import com.jinghang.capital.api.dto.BankChannel;
import com.jinghang.common.util.CollectionUtil;
import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.enums.RepayState;
import com.maguo.loan.cash.flow.job.AbstractJobHandler;
import com.maguo.loan.cash.flow.job.JobParam;
import com.maguo.loan.cash.flow.repository.RepayPlanRepository;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.listener.BatchRepayRepayListener;

import com.xxl.job.core.handler.annotation.JobHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/10/10
 * 还款计划到期扣款
 */
@Component
@JobHandler("repayPlanDueBatch")
public class RepayPlanDueBatch extends AbstractJobHandler {
    private static final Logger logger = LoggerFactory.getLogger(RepayPlanDueBatch.class);

    @Autowired
    private RepayPlanRepository repayPlanRepository;

    @Autowired
    private MqService mqService;

    @Override
    public void doJob(JobParam jobParam) {
        logger.info("repayPlanDueBatch jobParam:{}", JsonUtil.toJsonString(jobParam));

        LocalDate date;
        List<BankChannel> bankChannels = null;
        if (jobParam == null) {
            date = LocalDate.now();
        } else {
            date = jobParam.getStartDate() == null ? LocalDate.now() : jobParam.getStartDate();
            bankChannels = jobParam.getBankChannels();
        }

        //到期还款借据id
        List<Map<String, String>> loanMap;
        if (CollectionUtil.isEmpty(bankChannels)) {
            loanMap = repayPlanRepository.findAllShouldRepay(date.plusDays(1), RepayState.NORMAL.name());
        } else {
            loanMap = repayPlanRepository.findAllShouldRepayByBankChannels(date.plusDays(1), RepayState.NORMAL.name(),
                bankChannels.stream().map(Enum::name).toList());
        }

        logger.info("repayPlanDueBatch bankChannels:{} , 共 {} 条", CollectionUtil.isEmpty(bankChannels) ? "ALL" : bankChannels, loanMap.size());

        for (Map<String, String> loan : loanMap) {
            BankChannel bankChannel = BankChannel.valueOf(loan.get("bank_channel"));
            mqService.submitDueBatchRepay(loan.get("id"), BatchRepayRepayListener.getBatchRepayQueue(bankChannel));
        }

        logger.info("repayPlanDueBatch end");
    }

}
