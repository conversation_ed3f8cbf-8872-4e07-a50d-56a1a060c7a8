package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.BankList;
import com.maguo.loan.cash.flow.enums.AbleStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;


public interface BankListRepository extends JpaRepository<BankList, Integer> {

    Optional<BankList> findByAbbr(String abbr);

    @Query("select b from BankList b where b.abbr = ?1 or b.oldAbbr = ?1")
    Optional<BankList> findByAbbrOrOldAbbr(String abbr);

    List<BankList> findBankListByStatus(AbleStatus status);
}
