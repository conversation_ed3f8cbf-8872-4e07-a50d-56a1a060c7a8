package com.maguo.loan.cash.flow.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;


/**
 * 生成二维码
 *
 * <AUTHOR> Zhu
 */
public class QRUtil {

    private static final int DEFAULT_WIDTH = 200;
    private static final int DEFAULT_HEIGHT = 200;
    private static final int DEFAULT_MARGIN = 4;


    public static String generateQRCodeImage(String text) {
        return generateQRCodeImage(text, DEFAULT_WIDTH, DEFAULT_HEIGHT, DEFAULT_MARGIN);
    }

    public static String generateQRCodeImage(String text, int width, int height, int margin) {
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        String base64Str;
        try {
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.MARGIN, margin);
            hints.put(EncodeHintType.CHARACTER_SET, StandardCharsets.UTF_8);
            BitMatrix bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height, hints);
            ByteArrayOutputStream pngOutputStream = new ByteArrayOutputStream();
            MatrixToImageWriter.writeToStream(bitMatrix, "JPG", pngOutputStream);
            byte[] pngData = pngOutputStream.toByteArray();
            //base64编码
            base64Str = Base64.getEncoder().encodeToString(pngData);
            base64Str = base64Str.replaceAll("\n", "").replaceAll("\r", "");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return base64Str;
    }

}
