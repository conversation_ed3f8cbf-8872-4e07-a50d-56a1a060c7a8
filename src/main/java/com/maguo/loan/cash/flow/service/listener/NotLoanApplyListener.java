package com.maguo.loan.cash.flow.service.listener;


import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.entity.Credit;
import com.maguo.loan.cash.flow.entity.Loan;
import com.maguo.loan.cash.flow.entity.UserInfo;
import com.maguo.loan.cash.flow.enums.ProcessState;
import com.maguo.loan.cash.flow.enums.SmsTemplate;
import com.maguo.loan.cash.flow.repository.CreditRepository;
import com.maguo.loan.cash.flow.repository.LoanRepository;
import com.maguo.loan.cash.flow.repository.OrderRepository;
import com.maguo.loan.cash.flow.repository.UserInfoRepository;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.SmsService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Component
public class NotLoanApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(NotLoanApplyListener.class);

    @Autowired
    private WarningService warningService;

    @Autowired
    private LoanRepository loanRepository;

    @Autowired
    private SmsService smsService;

    @Autowired
    private UserInfoRepository userInfoRepository;

    @Autowired
    private CreditRepository creditRepository;

    @Autowired
    private OrderRepository orderRepository;

    public NotLoanApplyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.NOT_LOAN_APPLY_SMS_SEND)
    public void notLoanApplyListener(Message message, Channel channel) {
        String creditId = new String(message.getBody(), StandardCharsets.UTF_8);
        Credit credit = creditRepository.findById(creditId).orElseThrow();
        try {

            //获取放款信息
            Loan loan = loanRepository.findTopByUserIdAndLoanStateOrderByCreatedTimeDesc(credit.getUserId(), ProcessState.INIT);
            //获取用户信息
            UserInfo user = userInfoRepository.findById(credit.getUserId()).orElseThrow();
            //判断有无放款表
            if (loan == null || StringUtils.isBlank(loan.getApplyTime().toString())) {
//                smsService.send(SmsTemplate.LOAN_APPLY_REMIND, Map.of(
//                    "name", user.getName(),
//                    "flowChannel", credit.getFlowChannel().name()
//                ), user.getMobile());
                logger.info("用户{}未发起放款id为{}", user.getName(), credit.getUserId());
            } else {
                logger.info("用户{}已发起放款id为{}", user.getName(), credit.getUserId());
            }
        } catch (Exception e) {
            warningService.warn("资方授信通过且未要款短信发送异常" + credit.getUserId(),
                msg -> logger.error("资方授信通过且未要款短信发送异常:{},", credit.getUserId(), e));
        } finally {
            ackMsg(credit.getUserId(), message, channel);
        }
    }
}
