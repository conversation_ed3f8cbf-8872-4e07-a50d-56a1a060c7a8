import request from '@/utils/request'

/** 前端可用额度策略及配置（循环额度） */

// 可用额度策略配置-分页
export function queryRevolvingStrategyList(data) {
  return request({
    url: '/revolving/strategy/paging',
    method: 'post',
    data
  })
}

// 可用额度策略配置-详情
export function queryRevolvingStrategyDetail(data) {
  return request({
    url: '/revolving/strategy/detail',
    method: 'post',
    data
  })
}

// 可用额度策略配置-保存
export function apiSaveRevolvingStrategy(data) {
  return request({
    url: '/revolving/strategy/save',
    method: 'post',
    data
  })
}

// 用额度策略配置-启用
export function apiRevolvingStrategyEnable(data) {
  return request({
    url: '/revolving/strategy/enable',
    method: 'post',
    data
  })
}

// 用额度策略配置-禁用
export function apiRevolvingStrategyDisable(data) {
  return request({
    url: '/revolving/strategy/disable',
    method: 'post',
    data
  })
}

// 可用额度策略配置-分箱 保存
export function apiSaveRevolvingStrategyRelate(data) {
  return request({
    url: '/revolving/strategy/relate/save',
    method: 'post',
    data
  })
}

// 可用额度策略配置-分箱 查询
export function queryRevolvingStrategyRelate(data) {
  return request({
    url: '/revolving/strategy/relate/query',
    method: 'post',
    data
  })
}
