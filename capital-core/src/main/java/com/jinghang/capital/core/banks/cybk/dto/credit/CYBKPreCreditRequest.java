package com.jinghang.capital.core.banks.cybk.dto.credit;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;

/**
 * 撞库请求参数
 * @公司 中数金智(上海)有限公司
 * @作者 Mr.sandman
 * @时间 2025/06/24 16:58
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)

public class CYBKPreCreditRequest extends CYBKBaseRequest {

  @JsonIgnore
  private static final CYBKTradeCode TRADE_CODE = CYBKTradeCode.COLLISION_URL;

  /**
   * 身份证MD5
   */
  private String idNoMD5;

  /**
   * 手机号MD5
   */
  private String mobileMD5;

  /**
   * 产品编码
   */
  private String productCode;

  public String getIdNoMD5() {
    return idNoMD5;
  }

  public void setIdNoMD5( String idNoMD5 ) {
    this.idNoMD5 = idNoMD5;
  }

  public String getMobileMD5() {
    return mobileMD5;
  }

  public void setMobileMD5( String mobileMD5 ) {
    this.mobileMD5 = mobileMD5;
  }

  public String getProductCode() {
    return productCode;
  }

  public void setProductCode( String productCode ) {
    this.productCode = productCode;
  }

  @Override
  public CYBKTradeCode getTradeCode() {
    return TRADE_CODE;
  }
}
