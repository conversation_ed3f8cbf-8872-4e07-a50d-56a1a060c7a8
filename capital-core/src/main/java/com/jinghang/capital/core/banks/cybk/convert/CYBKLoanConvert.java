package com.jinghang.capital.core.banks.cybk.convert;

import com.cycfc.base.vo.Image;

import com.jinghang.capital.core.banks.cybk.dto.credit.CYBKFileInfo;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLoanApplyRequest;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLoanApplyResponse;
import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLoanQueryResponse;
import com.jinghang.capital.core.banks.cybk.dto.repay.CYBKRepayPlanInfo;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.vo.loan.LoanResultVo;
import com.jinghang.capital.core.vo.repay.PlanItemVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/20
 */
@Mapper(uses = {CYBKEnumConvert.class})
public interface CYBKLoanConvert {
    CYBKLoanConvert INSTANCE = Mappers.getMapper(CYBKLoanConvert.class);

    CYBKLoanApplyRequest toReq(Loan loan);

    @Mapping(source = "loanSeq", target = "loanNo")
    LoanResultVo toVo(CYBKLoanApplyResponse response);


    @Mapping(source = "dnSts", target = "status", qualifiedByName = "toProcessStatus")
    @Mapping(source = "payMsg", target = "failMsg")
    @Mapping(source = "loanSeq", target = "loanNo")
    @Mapping(source = "contractNo", target = "loanContractNo")
    @Mapping(source = "loanActvTime", target = "loanTime", dateFormat = "yyyy-MM-dd HH:mm:ss")
    LoanResultVo toVo(CYBKLoanQueryResponse response);

    @Mapping(source = "psPrcpAmt", target = "principalAmt")
    @Mapping(source = "psNormIntAmt", target = "interestAmt")
    @Mapping(source = "psOdIntAmt", target = "penaltyAmt")
    @Mapping(source = "guaraFeeAmt", target = "guaranteeAmt")
    @Mapping(source = "dueDt", target = "repayDate")
    @Mapping(source = "perdNo", target = "period")
    @Mapping(constant = "0", target = "breachAmt")
    PlanItemVo toPlanItemVo(CYBKRepayPlanInfo repayPlanInfo);

    List<PlanItemVo> toPlanVo(List<CYBKRepayPlanInfo> repayPlanInfos);

    Image toImage(CYBKFileInfo fileInfo);

    @Named("toProcessStatus")
    default ProcessStatus toProcessStatus(String cybkStatus) {
        return switch (cybkStatus) {
            case "100" -> ProcessStatus.PROCESSING;
            case "200" -> ProcessStatus.SUCCESS;
            case "300" -> ProcessStatus.FAIL;
            default -> throw new IllegalArgumentException("Unexpected response status:" + cybkStatus);
        };
    }
}
