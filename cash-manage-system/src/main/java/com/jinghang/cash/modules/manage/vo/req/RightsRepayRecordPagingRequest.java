package com.jinghang.cash.modules.manage.vo.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.cash.modules.manage.vo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-12-30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RightsRepayRecordPagingRequest extends PageParam {

    private String mobile;

    private String repaymentNo;

    private String businessNo;

    private String paymentChannelCode;

    private String rightsSupplier;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
}
