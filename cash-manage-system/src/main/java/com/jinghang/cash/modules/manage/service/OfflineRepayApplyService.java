package com.jinghang.cash.modules.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.jinghang.cash.modules.manage.vo.req.OfflineRepayApplyRequest;
import com.jinghang.cash.modules.manage.vo.res.OfflineRepayApplyResponse;
import com.jinghang.cash.pojo.OfflineRepayApply;

/**
 * <AUTHOR>
 */
public interface OfflineRepayApplyService extends IService<OfflineRepayApply> {
    /**
     * 销账记录-列表分页查询
     */
    PageInfo<OfflineRepayApplyResponse> searchPage(OfflineRepayApplyRequest reqVO);
}
