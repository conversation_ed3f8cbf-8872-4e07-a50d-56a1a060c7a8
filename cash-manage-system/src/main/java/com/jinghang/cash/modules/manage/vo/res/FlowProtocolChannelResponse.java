package com.jinghang.cash.modules.manage.vo.res;

import com.jinghang.cash.enums.ProtocolChannel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class FlowProtocolChannelResponse {

    private String flowChannel;

    /**
     * 第一绑卡渠道
     */
    private ProtocolChannel firstProtocolChannel;

    /**
     * 第二绑卡渠道
     */
    private ProtocolChannel secondProtocolChannel;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedTime;

}
