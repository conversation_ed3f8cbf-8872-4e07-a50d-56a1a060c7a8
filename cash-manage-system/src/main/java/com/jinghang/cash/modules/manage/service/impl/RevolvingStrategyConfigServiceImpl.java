package com.jinghang.cash.modules.manage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jinghang.cash.mapper.RevolvingStrategyRelateMapper;
import com.jinghang.cash.mapper.RevolvingStrategyConfigMapper;
import com.jinghang.cash.modules.manage.mapstruct.ManageMapstruct;
import com.jinghang.cash.modules.manage.remote.RevolvingStrategyFeignService;
import com.jinghang.cash.modules.manage.service.RevolvingStrategyConfigService;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyRelateQueryRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyRelateSaveRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyConfigDetailRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyConfigPagingRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyConfigSaveRequest;
import com.jinghang.cash.modules.manage.vo.req.RevolvingStrategyConfigStatusRequest;
import com.jinghang.cash.modules.manage.vo.res.RevolvingStrategyRelateResponse;
import com.jinghang.cash.modules.manage.vo.res.RevolvingStrategyConfigResponse;
import com.jinghang.cash.pojo.RevolvingStrategyRelate;
import com.jinghang.cash.pojo.RevolvingStrategyConfig;
import com.jinghang.cash.utils.SecurityUtils;
import com.jinghang.common.util.StringUtil;
import com.jinghang.ppd.api.dto.RestResult;
import com.jinghang.ppd.api.dto.revolving.RevolvingStrategyChangeStatusDTO;
import com.jinghang.ppd.api.dto.revolving.RevolvingStrategyRelateSaveDTO;
import com.jinghang.ppd.api.dto.revolving.RevolvingStrategySaveDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025-02-19
 */
@Service
@Slf4j
public class RevolvingStrategyConfigServiceImpl extends ServiceImpl<RevolvingStrategyConfigMapper, RevolvingStrategyConfig> implements RevolvingStrategyConfigService {

    @Autowired
    private RevolvingStrategyRelateMapper revolvingStrategyRelateMapper;

    @Autowired
    private RevolvingStrategyFeignService revolvingStrategyFeignService;

    @Override
    public PageInfo<RevolvingStrategyConfigResponse> paging(RevolvingStrategyConfigPagingRequest request) {
        Page<Object> page = PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<RevolvingStrategyConfig> list = list(
            new LambdaQueryWrapper<RevolvingStrategyConfig>()
                .eq(StringUtil.isNotBlank(request.getStrategyState()), RevolvingStrategyConfig::getStrategyState, request.getStrategyState())
                .orderByDesc(RevolvingStrategyConfig::getId)
        );
        // 更新时间处理
        List<String> strategyIds = list.stream().map(RevolvingStrategyConfig::getId).toList();
        if (!strategyIds.isEmpty()) {
            List<RevolvingStrategyRelate> revolvingStrategyRelates = revolvingStrategyRelateMapper.selectList(
                new LambdaQueryWrapper<RevolvingStrategyRelate>()
                    .in(RevolvingStrategyRelate::getStrategyId, strategyIds)
            );
            Map<String, List<RevolvingStrategyRelate>> relateMap = revolvingStrategyRelates.stream()
                .collect(Collectors.groupingBy(RevolvingStrategyRelate::getStrategyId));
            for (RevolvingStrategyConfig config : list) {
                List<RevolvingStrategyRelate> revolvingStrategyRelateList = relateMap.get(config.getId());
                if (revolvingStrategyRelateList != null && !revolvingStrategyRelateList.isEmpty()) {
                    RevolvingStrategyRelate relate = revolvingStrategyRelateList.get(0);
                    if (relate.getUpdatedTime().isAfter(config.getUpdatedTime())) {
                        config.setUpdatedTime(relate.getUpdatedTime());
                    }
                }
            }
        }
        List<RevolvingStrategyConfigResponse> responseList = ManageMapstruct.INSTANCE.toRevolvingStrategyConfigResponseList(list);
        PageInfo<RevolvingStrategyConfigResponse> pageInfo = new PageInfo<>(responseList);
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        return pageInfo;
    }

    @Override
    public void save(RevolvingStrategyConfigSaveRequest request) {
        // cash-business
        RevolvingStrategySaveDTO saveDTO = new RevolvingStrategySaveDTO();
        saveDTO.setId(request.getId());
        saveDTO.setStrategyName(request.getStrategyName());
        saveDTO.setStrategyCode(request.getStrategyCode());
        saveDTO.setAmountLower(request.getAmountLower());
        saveDTO.setAmountUpper(request.getAmountUpper());
        saveDTO.setOperator(SecurityUtils.getCurrentUsername());
        RestResult<Boolean> result = revolvingStrategyFeignService.save(saveDTO);
        if (result == null || !result.isSuccess()) {
            String reason = result == null ? "系统异常" : result.getMsg();
            log.error("循环额度策略保存异常, reason: {}", reason);
            throw new RuntimeException(reason);
        }
    }

    @Override
    public RevolvingStrategyConfigResponse detail(RevolvingStrategyConfigDetailRequest request) {
        RevolvingStrategyConfig config = getById(request.getId());
        return ManageMapstruct.INSTANCE.toRevolvingStrategyConfigResponse(config);
    }

    @Override
    public void enable(RevolvingStrategyConfigStatusRequest request) {
        // cash-business
        RevolvingStrategyChangeStatusDTO changeStatusDTO = new RevolvingStrategyChangeStatusDTO();
        changeStatusDTO.setId(request.getId());
        changeStatusDTO.setStrategyState("Y");
        changeStatusDTO.setOperator(SecurityUtils.getCurrentUsername());
        RestResult<Boolean> result = revolvingStrategyFeignService.changeStatus(changeStatusDTO);
        if (result == null || !result.isSuccess()) {
            String reason = result == null ? "系统异常" : result.getMsg();
            log.error("循环额度策略启用异常, reason: {}", reason);
            throw new RuntimeException(reason);
        }
    }

    @Override
    public void disable(RevolvingStrategyConfigStatusRequest request) {
        // cash-business
        RevolvingStrategyChangeStatusDTO changeStatusDTO = new RevolvingStrategyChangeStatusDTO();
        changeStatusDTO.setId(request.getId());
        changeStatusDTO.setStrategyState("N");
        changeStatusDTO.setOperator(SecurityUtils.getCurrentUsername());
        RestResult<Boolean> result = revolvingStrategyFeignService.changeStatus(changeStatusDTO);
        if (result == null || !result.isSuccess()) {
            String reason = result == null ? "系统异常" : result.getMsg();
            log.error("循环额度策略停用异常, reason: {}", reason);
            throw new RuntimeException(reason);
        }
    }

    @Override
    public void relateSave(RevolvingStrategyRelateSaveRequest request) {
        // cash-business
        RevolvingStrategyRelateSaveDTO binningSaveDTO = new RevolvingStrategyRelateSaveDTO();
        binningSaveDTO.setStrategyId(request.getStrategyId());
        binningSaveDTO.setOperator(SecurityUtils.getCurrentUsername());
        List<RevolvingStrategyRelateSaveDTO.Relate> binningList = new ArrayList<>();
        for (RevolvingStrategyRelateSaveRequest.Relate relate : request.getRelateDetail()) {
            RevolvingStrategyRelateSaveDTO.Relate binningDTO = new RevolvingStrategyRelateSaveDTO.Relate();
            binningDTO.setId(relate.getId());
            binningDTO.setRelateCode(relate.getRelateCode());
            binningDTO.setFrontAmount(relate.getFrontAmount());
            binningDTO.setRatio(relate.getRatio());
            binningList.add(binningDTO);
        }
        binningSaveDTO.setBinningDetail(binningList);
        RestResult<Boolean> result = revolvingStrategyFeignService.relateSave(binningSaveDTO);
        if (result == null || !result.isSuccess()) {
            String reason = result == null ? "系统异常" : result.getMsg();
            log.error("循环额度策略分箱保存异常, reason: {}", reason);
            throw new RuntimeException(reason);
        }
    }

    @Override
    public List<RevolvingStrategyRelateResponse> relateQuery(RevolvingStrategyRelateQueryRequest request) {
        List<RevolvingStrategyRelate> revolvingStrategyRelateList = revolvingStrategyRelateMapper.selectList(
            new LambdaQueryWrapper<RevolvingStrategyRelate>()
                .eq(RevolvingStrategyRelate::getStrategyId, request.getStrategyId())
                .orderByAsc(RevolvingStrategyRelate::getRelateCode)
        );
        return ManageMapstruct.INSTANCE.toRevolvingStrategyRelateResponseList(revolvingStrategyRelateList);
    }
}
